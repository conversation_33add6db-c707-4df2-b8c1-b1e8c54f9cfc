#---------------------------------#
#      general configuration      #
#---------------------------------#

# version format
version: "{branch} (#{build})"

# branches to build
branches:
  only:
    - master

#---------------------------------#
#    environment configuration    #
#---------------------------------#

# scripts that are called at very beginning, before repo cloning
init:
  - git config --global core.autocrlf true
  - ps: $env:GIT_HASH=$env:APPVEYOR_REPO_COMMIT.Substring(0, 10)

# clone directory
clone_folder: c:\projects\NonBlockingRtttl

# scripts that run after cloning repository
install:
- cmd: call %APPVEYOR_BUILD_FOLDER%\ci\appveyor\arduino_ide_install.bat
- cmd: call %APPVEYOR_BUILD_FOLDER%\ci\appveyor\install.bat

#---------------------------------#
#       build configuration       #
#---------------------------------#

build_script:
- cmd: call %APPVEYOR_BUILD_FOLDER%\ci\appveyor\arduino_build_example.bat rtttl_blocking
- cmd: call %APPVEYOR_BUILD_FOLDER%\ci\appveyor\arduino_build_example.bat rtttl_demo
