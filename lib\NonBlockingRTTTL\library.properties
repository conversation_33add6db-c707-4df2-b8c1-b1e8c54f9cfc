name=NonBlockingRTTTL
version=1.3.0
author=<PERSON> <<EMAIL>>
maintainer=<PERSON> <<EMAIL>>
sentence=Non-blocking Arduino library for playing RTTTL melodies.
paragraph=The library allows your program to read or write IOs pins while playing. Implementing "stop" or "next song" push buttons is really easy!
category=Other
url=https://github.com/end2endzone/NonBlockingRTTTL
architectures=*
includes=NonBlockingRtttl.h
