# Install

The library can be found, installed, or updated from the Arduino IDE using the official Arduino Library Manager (available from IDE version 1.6.2).


The library can be installed on the system by following the same steps as with other Arduino library.

Refer to [Installing Additional Arduino Libraries](https://www.arduino.cc/en/Guide/Libraries) tutorial for details on how to install a third party library.




# Build

The library does not require building before usage.



# Testing #

The library does not provide any unit tests.
