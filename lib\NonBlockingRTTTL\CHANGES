Changes for 1.3.0:

* Fixed issue #10 - Contradiction in the RTTTL specification.

Changes for 1.2.2:

* Fixed issue #8 - Adds replacement for noTone() and tone() for ESP32

Changes for 1.2.1:

* Fixed issue #5 - Update INSTALL.md to use the Arduino Library Manager.
* Fixed issue #6 - Bug for ESP8266 environment - noTone() not called at end of sound.

Changes for 1.2.0:

* Fixed issue #3 - Move library to the root of the repository
* New feature: Now using <PERSON><PERSON><PERSON><PERSON><PERSON> and Travis CI to build examples for validing code compilation.

Changes for 1.1.1:

* Updated README and INSTALL instructions for more clarity

Changes for 1.1.0:

* Updated documentation

Changes for 1.0.101:

* First public release.
* Code originally release at http://www.end2endzone.com/nonblockingrtttl-a-non-blocking-arduino-library-for-playing-rtttl-melodies/
