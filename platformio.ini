; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:nrf52840_dk_adafruit]
platform = nordicnrf52
board = ul_840_board
framework = arduino
; extra_scripts = extra_script.py
build_type = release
build_flags =
    ; -Wall 
    ; -Wextra 
    ; -Werror     
monitor_speed = 115200
lib_deps = 
    koendv/RTT Stream @ ^1.4.1
	adafruit/Adafruit NeoPixel @ ^1.12.0
    sandeepmistry/LoRa @ ^0.8.0
    jgromes/RadioLib @ ^7.1.2
; upload_protocol = jlink