/***************************************************************************
 *   @file   ade7816.h
 *   @brief  Header file of ADE7816 Driver.
 *   <AUTHOR> (<EMAIL>)
 ********************************************************************************
 * Copyright 2024(c) Analog Devices, Inc.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice,
 *    this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * 3. Neither the name of Analog Devices, Inc. nor the names of its
 *    contributors may be used to endorse or promote products derived from this
 *    software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY ANALOG DEVICES, INC. “AS IS” AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
 * EVENT SHALL ANALOG DEVICES, INC. BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
 * OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 ******************************************************************************/
#ifndef __ADE7816_H__
#define __ADE7816_H__

#include <stdbool.h>
#include <stdint.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <inttypes.h>
#include "no_os_util.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

#include "driver/gpio.h"
#include "driver/i2c_master.h"
#include "driver/spi_master.h"

#define ADE7816_I2C_ADDR            0x38

// 增益寄存器组
#define ADE7816_VGAIN_REG           0x4380  // 电压通道增益寄存器
#define ADE7816_IAGAIN_REG          0x4381  // A相电流通道增益寄存器
#define ADE7816_IBGAIN_REG          0x4382  // B相电流通道增益寄存器
#define ADE7816_ICGAIN_REG          0x4383  // C相电流通道增益寄存器
#define ADE7816_IDGAIN_REG          0x4384  // D相电流通道增益寄存器
#define ADE7816_IEGAIN_REG          0x4385  // E相电流通道增益寄存器
#define ADE7816_IFGAIN_REG          0x4386  // F相电流通道增益寄存器

#define ADE7816_DICOEFF_REG         0x4388  // 数字积分器系数寄存器
#define ADE7816_HPFDIS_REG          0x4389  // 高通滤波器禁用寄存器
#define ADE7816_VRMSOS_REG          0x438A  // 电压通道RMS偏移校正寄存器
#define ADE7816_IARMSOS_REG         0x438B  // A相电流RMS偏移校正寄存器
#define ADE7816_IBRMSOS_REG         0x438C  // B相电流RMS偏移校正寄存器
#define ADE7816_ICRMSOS_REG         0x438D  // C相电流RMS偏移校正寄存器
#define ADE7816_IDRMSOS_REG         0x438E  // D相电流RMS偏移校正寄存器
#define ADE7816_IERMSOS_REG         0x438F  // E相电流RMS偏移校正寄存器
#define ADE7816_IFRMSOS_REG         0x4390  // F相电流RMS偏移校正寄存器

// 有功功率校正寄存器组
#define ADE7816_AWGAIN_REG          0x4391  // A相有功功率增益校正寄存器
#define ADE7816_AWATTOS_REG         0x4392  // A相有功功率偏移校正寄存器
#define ADE7816_BWGAIN_REG          0x4393  // B相有功功率增益校正寄存器
#define ADE7816_BWATTOS_REG         0x4394  // B相有功功率偏移校正寄存器
#define ADE7816_CWGAIN_REG          0x4395  // C相有功功率增益校正寄存器
#define ADE7816_CWATTOS_REG         0x4396  // C相有功功率偏移校正寄存器
#define ADE7816_DWGAIN_REG          0x4397  // D相有功功率增益校正寄存器
#define ADE7816_DWATTOS_REG         0x4398  // D相有功功率偏移校正寄存器
#define ADE7816_EWGAIN_REG          0x4399  // E相有功功率增益校正寄存器
#define ADE7816_EWATTOS_REG         0x439A  // E相有功功率偏移校正寄存器
#define ADE7816_FWGAIN_REG          0x439B  // F相有功功率增益校正寄存器
#define ADE7816_FWATTOS_REG         0x439C  // F相有功功率偏移校正寄存器

// 无功功率校正寄存器组
#define ADE7816_AVARGAIN_REG        0x439D  // A相无功功率增益校正寄存器
#define ADE7816_AVAROS_REG          0x439E  // A相无功功率偏移校正寄存器
#define ADE7816_BVARGAIN_REG        0x439F  // B相无功功率增益校正寄存器
#define ADE7816_BVAROS_REG          0x43A0  // B相无功功率偏移校正寄存器
#define ADE7816_CVARGAIN_REG        0x43A1  // C相无功功率增益校正寄存器
#define ADE7816_CVAROS_REG          0x43A2  // C相无功功率偏移校正寄存器
#define ADE7816_DVARGAIN_REG        0x43A3  // D相无功功率增益校正寄存器
#define ADE7816_DVAROS_REG          0x43A4  // D相无功功率偏移校正寄存器
#define ADE7816_EVARGAIN_REG        0x43A5  // E相无功功率增益校正寄存器
#define ADE7816_EVAROS_REG          0x43A6  // E相无功功率偏移校正寄存器
#define ADE7816_FVARGAIN_REG        0x43A7  // F相无功功率增益校正寄存器
#define ADE7816_FVAROS_REG          0x43A8  // F相无功功率偏移校正寄存器

// 阈值和系数寄存器组
#define ADE7816_WTHR1_REG           0x43AB  // 有功功率阈值1寄存器
#define ADE7816_WTHR0_REG           0x43AC  // 有功功率阈值0寄存器
#define ADE7816_VARTHR1_REG         0x43AD  // 无功功率阈值1寄存器
#define ADE7816_VARTHR0_REG         0x43AE  // 无功功率阈值0寄存器
#define ADE7816_APNOLOAD_REG        0x43AF  // 有功功率空载阈值寄存器
#define ADE7816_VARNOLOAD_REG       0x43B0  // 无功功率空载阈值寄存器
#define ADE7816_PCF_A_COEFF_REG     0x43B1  // A相功率校正因数系数寄存器
#define ADE7816_PCF_B_COEFF_REG     0x43B3  // B相功率校正因数系数寄存器
#define ADE7816_PCF_C_COEFF_REG     0x43B4  // C相功率校正因数系数寄存器
#define ADE7816_PCF_D_COEFF_REG     0x43B5  // D相功率校正因数系数寄存器
#define ADE7816_PCF_E_COEFF_REG     0x43B6  // E相功率校正因数系数寄存器
#define ADE7816_PCF_F_COEFF_REG     0x43B7  // F相功率校正因数系数寄存器

// RMS测量寄存器组
#define ADE7816_VRMS_REG            0x43C0  // 电压有效值寄存器
#define ADE7816_IARMS_REG           0x43C1  // A相电流有效值寄存器
#define ADE7816_IBRMS_REG           0x43C2  // B相电流有效值寄存器
#define ADE7816_ICRMS_REG           0x43C3  // C相电流有效值寄存器
#define ADE7816_IDRMS_REG           0x43C4  // D相电流有效值寄存器
#define ADE7816_IERMS_REG           0x43C5  // E相电流有效值寄存器
#define ADE7816_IFRMS_REG           0x43C6  // F相电流有效值寄存器

#define ADE7816_RUN_REG             0xE228  // 运行寄存器(启动/停止DSP)

// 能量累加寄存器组
#define ADE7816_AWATTHR_REG         0xE400  // A相有功能量累加寄存器
#define ADE7816_BWATTHR_REG         0xE401  // B相有功能量累加寄存器
#define ADE7816_CWATTHR_REG         0xE402  // C相有功能量累加寄存器
#define ADE7816_DWATTHR_REG         0xE403  // D相有功能量累加寄存器
#define ADE7816_EWATTHR_REG         0xE404  // E相有功能量累加寄存器
#define ADE7816_FWATTHR_REG         0xE405  // F相有功能量累加寄存器
#define ADE7816_AVARHR_REG          0xE406  // A相无功能量累加寄存器
#define ADE7816_BVARHR_REG          0xE407  // B相无功能量累加寄存器
#define ADE7816_CVARHR_REG          0xE408  // C相无功能量累加寄存器
#define ADE7816_DVARHR_REG          0xE409  // D相无功能量累加寄存器
#define ADE7816_EVARHR_REG          0xE40A  // E相无功能量累加寄存器
#define ADE7816_FVARHR_REG          0xE40B  // F相无功能量累加寄存器

// 状态和监测寄存器组
#define ADE7816_IPEAK_REG           0xE500  // 电流峰值检测寄存器
#define ADE7816_VPEAK_REG           0xE501  // 电压峰值检测寄存器
#define ADE7816_STATUS0_REG         0xE502  // 状态寄存器0
#define ADE7816_STATUS1_REG         0xE503  // 状态寄存器1

// 保护和中断配置寄存器组
#define ADE7816_OIVL_REG            0xE507  // 过流阈值寄存器
#define ADE7816_OVLVL_REG           0xE508  // 过压阈值寄存器
#define ADE7816_SAGLVL_REG          0xE509  // 电压暂降阈值寄存器
#define ADE7816_MASK0_REG           0xE50A  // 中断屏蔽寄存器0
#define ADE7816_MASK1_REG           0xE50B  // 中断屏蔽寄存器1
#define ADE7816_IAVW_IDVW_REG       0xE50C  // A/D相电流矢量和寄存器
#define ADE7816_IBVW_IEVW_REG       0xE50D  // B/E相电流矢量和寄存器
#define ADE7816_ICVW_IFVW_REG       0xE50E  // C/F相电流矢量和寄存器

#define ADE7816_VWV_REG             0xE510  // 电压波形值寄存器

#define ADE7816_CHECKSUM_REG        0xE51F  // 校验和寄存器

// 相位和状态寄存器组
#define ADE7816_CHSTATUS_REG        0xE600  // 通道状态寄存器
#define ADE7816_ANGLE0_REG          0xE601  // 相角0寄存器
#define ADE7816_ANGLE1_REG          0xE602  // 相角1寄存器
#define ADE7816_ANGLE2_REG          0xE603  // 相角2寄存器

#define ADE7816_PERIOD_REG          0xE607  // 线路周期测量寄存器
#define ADE7816_CHNOLOAD_REG        0xE608  // 通道空载检测寄存器

// 配置寄存器组
#define ADE7816_LINECYC_REG         0xE60C  // 线路周期计数寄存器
#define ADE7816_ZXTOUT_REG          0xE60D  // 过零超时寄存器
#define ADE7816_COMPMODE_REG        0xE60E  // 计算模式寄存器
#define ADE7816_GAIN_REG            0xE60F  // ADC增益配置寄存器

#define ADE7816_CHSIGN_REG          0xE617  // 通道符号寄存器
#define ADE7816_CONFIG_REG          0xE618  // 配置寄存器

// 测量模式配置寄存器组
#define ADE7816_MMODE_REG           0xE700  // 测量模式寄存器
#define ADE7816_ACCMODE_REG         0xE701  // 累加模式寄存器
#define ADE7816_LCYCMODE_REG        0xE702  // 线路周期累加模式寄存器
#define ADE7816_PEAKCYC_REG         0xE703  // 峰值检测周期配置寄存器
#define ADE7816_SAGCYC_REG          0xE704  // 电压暂降周期配置寄存器

#define ADE7816_HSDC_CFG_REG        0xE706  // 高速数据采集配置寄存器
#define ADE7816_VERSION_REG         0xE707  // 版本寄存器

#define ADE7816_CONFIG2_REG         0xEC01  // 配置寄存器2

// MASK MACROS
#define ADE7816_LSB_REG_MASK        NO_OS_GENMASK(7, 0)
#define ADE7816_MSB_REG_MASK        NO_OS_GENMASK(15, 8)

#define ADE7816_FOURTH_BYTE_MASK    NO_OS_GENMASK(31, 24)
#define ADE7816_THIRD_BYTE_MASK     NO_OS_GENMASK(23, 16)
#define ADE7816_SECOND_BYTE_MASK    NO_OS_GENMASK(15, 8)
#define ADE7816_FIRST_BYTE_MASK     NO_OS_GENMASK(7, 0)

#define ADE7816_SWRST_MASK          NO_OS_BIT(7)

#define ADE7816_I2C_LOCK_MASK       NO_OS_BIT(1)

// 启用积分器MASK
#define ADE7816_INTEN_MASK          NO_OS_BIT(0)

#define ADE7816_ZSPE_MASK           NO_OS_GENMASK(27, 0)

#define ADE7816_THR_MSB_MASK        NO_OS_GENMASK(31, 24)
#define ADE7816_THR_LSB_MASK        NO_OS_GENMASK(23, 0)

#define ADE7816_RSTREAD_MASK        NO_OS_BIT(6)

#define ADE7816_LMASK               NO_OS_GENMASK(1, 0)
#define ADE7816_ZX_SEL_MASK         NO_OS_BIT(3)

#define ADE7816_LENERGY_MASK        NO_OS_BIT(5)

#define ADE7816_CHANNEL_SEL_MASK    NO_OS_BIT(14)

#define ADE7816_DREADY_MASK         NO_OS_BIT(17)

#define ADE7816_MMODE_MASK          NO_OS_GENMASK(4, 2)

#define ADE7816_REVSEL_MASK         NO_OS_GENMASK(7, 6)

#define ADE7816_ANGLESEL_MASK       NO_OS_GENMASK(10, 9)

#define ADE7816_SIGN_MASK(x)        (((x) > 3) ? ((x) - 4) : (x))

#define ADE7816_INT_MASK(x)         NO_OS_BIT(x)

#define ADE7816_INIT_DELAY          40
#define ADE7816_HWRST_DELAY         10

#define ADE7816_LINECYC_VAL(enable) ((enable) ? 0x11 : 0x00)

#define ADE7816_CHECKSUM_VAL        0x33666787

enum ade7816_comm_type
{
    ADE7816_I2C,
    ADE7816_SPI
};

enum ade7816_channel
{
    ADE7816_CHANNEL_VOLTAGE,
    ADE7816_CHANNEL_A,
    ADE7816_CHANNEL_B,
    ADE7816_CHANNEL_C,
    ADE7816_CHANNEL_D,
    ADE7816_CHANNEL_E,
    ADE7816_CHANNEL_F,
};

enum ade7816_gain_type
{
    ADE7816_CURRENT_GAIN,
    ADE7816_ACTIVE_POWER_GAIN,
    ADE7816_REACTIVE_POWER_GAIN,
};

enum ade7816_mmode_sel
{
    ADE7816_PEAKSEL_0_AD,
    ADE7816_PEAKSEL_1_BE,
    ADE7816_PEAKSEL_2_CF,
};

enum ade7816_angle_sel
{
    ADE7816_VOLTAGE_CURRENT,
    ADE7816_CURRENT_CURRENT = 2,
};

enum ade7816_status_int
{
    ADE7816_AEHF1_INT,
    ADE7816_AEHF2_INT,
    ADE7816_REHF1_INT,
    ADE7816_REHF2_INT,
    ADE7816_LENERGY_INT = 5,
    ADE7816_REVAP1_INT,
    ADE7816_REVAP2_INT,
    ADE7816_REVAP3_INT,
    ADE7816_REVRP1_INT = 10,
    ADE7816_REVRP2_INT,
    ADE7816_REVRP3_INT,
    ADE7816_DREADY_INT = 17,
    ADE7816_NLOAD1_INT = 32,
    ADE7816_NLOAD2_INT,
    ADE7816_ZXTOV_INT  = 35,
    ADE7816_ZXTOI1_INT = 38,
    ADE7816_ZXTOI2_INT,
    ADE7816_ZXTOI3_INT,
    ADE7816_ZXV_INT,
    ADE7816_ZXI1_INT = 44,
    ADE7816_ZXI2_INT,
    ADE7816_ZXI3_INT,
    ADE7816_RSTDONE_INT,
    ADE7816_SAG_INT,
    ADE7816_OI_INT,
    ADE7816_OV_INT,
    ADE7816_PKI_INT = 55,
    ADE7816_PKV_INT,
};

enum ade7816_active_irq
{
    ADE7816_NO_IRQ,
    ADE7816_IRQ0,
    ADE7816_IRQ1,
    ADE7816_IRQ0_IRQ1,
};

enum ade7816_pcf_coeff
{
    ADE7816_PCF_50HZ = 0x400CA4,
    ADE7816_PCF_60HZ = 0x401235,
};

struct gpio_param
{
    gpio_config_t    gpio_config;
    gpio_num_t       gpio_num;
    gpio_mode_t      mode;
    gpio_pull_mode_t pull_mode;
    gpio_int_type_t  int_type;
    gpio_isr_t      *isr_handler;
};

struct i2c_param
{
    i2c_master_bus_config_t i2c_bus_config;
    i2c_master_bus_handle_t i2c_master_handle;
    i2c_master_dev_handle_t i2c_device_handle;
    i2c_device_config_t     i2c_device_config;
    uint8_t                 addr_wordlen;
    uint32_t                write_time_ms;
};

struct ade7816_desc;

typedef int (*ade7816_reg_read)(struct ade7816_desc *desc, uint16_t reg, uint32_t *val);
typedef int (*ade7816_reg_write)(struct ade7816_desc *desc, uint16_t reg, uint32_t val);

struct ade7816_init_param
{
    struct i2c_param  i2c_param;
    struct gpio_param reset_param;
    struct gpio_param ss_param;

    enum ade7816_comm_type comm_type;
};

struct ade7816_desc
{
    struct i2c_param *i2c_desc;

    struct gpio_param *reset_desc;
    struct gpio_param *ss_desc;

    ade7816_reg_read  reg_read;
    ade7816_reg_write reg_write;

    enum ade7816_active_irq active_irq;
    enum ade7816_comm_type  comm_type;
    enum ade7816_channel    chan;

    uint32_t status0;
    uint32_t status1;

    bool lcycle_mode;
};

/** ADE7816 I2C Read Register function. */
int ade7816_i2c_reg_read(struct ade7816_desc *desc, uint16_t reg, uint32_t *val);

/** ADE7816 I2C Write Register function. */
int ade7816_i2c_reg_write(struct ade7816_desc *desc, uint16_t reg, uint32_t val);

/** Register read wrapper function. */
int ade7816_read_reg(struct ade7816_desc *desc, uint16_t reg, uint32_t *val);

/** Register write wrapper function. */
int ade7816_write_reg(struct ade7816_desc *desc, uint16_t reg, uint32_t val);

/** Register update function. */
int ade7816_reg_update(struct ade7816_desc *desc, uint16_t reg, uint32_t mask, uint32_t val);

/** ADE7816 software reset function. */
int ade7816_sw_reset(struct ade7816_desc *desc);

/** ADE7816 hardware reset function. */
int ade7816_hw_reset(struct ade7816_desc *desc);

/** ADE7816 set communication function. */
int ade7816_set_comm(struct ade7816_desc *desc, enum ade7816_comm_type type);

/** ADE7816 read active energy function. */
int ade7816_read_active_energy(struct ade7816_desc *desc, enum ade7816_channel chan, int32_t *val);

/** ADE7816 set active energy threshold value function. */
int ade7816_set_active_thr(struct ade7816_desc *desc, uint16_t freq);

/** ADE7816 get active energy threshold value function. */
int ade7816_get_active_thr(struct ade7816_desc *desc, uint16_t *freq);

/** ADE7816 read reactive energy value function. */
int ade7816_read_reactive_energy(struct ade7816_desc *desc, enum ade7816_channel chan, int32_t *val);

/** ADE7816 set reactive energy threshold value function. */
int ade7816_set_reactive_thr(struct ade7816_desc *desc, uint16_t freq);

/** ADE7816 get reactive energy threshold value function. */
int ade7816_get_reactive_thr(struct ade7816_desc *desc, uint16_t *freq);

/** ADE7816 set line cycle mode function. */
int ade7816_set_lcycle_mode(struct ade7816_desc *desc, bool enable, uint16_t cycles, bool lenergy);

/** ADE7816 read root mean square measurement value function. */
int ade7816_read_rms(struct ade7816_desc *desc, enum ade7816_channel chan, uint32_t *rms);

/** ADE7816 set no load condition function. */
int ade7816_set_no_load(struct ade7816_desc *desc, uint16_t voltage, uint16_t current, bool enable);

/** ADE7816 set gain value for channel function. */
int ade7816_set_gain(struct ade7816_desc *desc, enum ade7816_channel chan, int16_t scale, enum ade7816_gain_type gain);

/** ADE7816 get gain value for channel function. */
int ade7816_get_gain(struct ade7816_desc *desc, enum ade7816_channel chan, int16_t *scale, enum ade7816_gain_type gain);

/** ADE7816 set offset value for channel function. */
int ade7816_set_offset(struct ade7816_desc *desc, enum ade7816_channel chan, int16_t scale, enum ade7816_gain_type gain);

/** ADE7816 get offset value for channel function. */
int ade7816_get_offset(struct ade7816_desc *desc, enum ade7816_channel chan, int16_t *scale, enum ade7816_gain_type gain);

/** ADE7816 set phase calibration coefficient function. */
int ade7816_set_phase(struct ade7816_desc *desc, enum ade7816_channel chan, enum ade7816_pcf_coeff pcf_coeff);

/** ADE7816 root mean square measurement calibration function. */
int ade7816_calib_rms(struct ade7816_desc *desc, enum ade7816_channel chan, int32_t rms);

/** ADE7816 group selection for channels function. */
int ade7816_group_sel(struct ade7816_desc *desc, enum ade7816_channel chan);

/** ADE7816 zero-crossing detection for channel function. */
int ade7816_zx_detect(struct ade7816_desc *desc, enum ade7816_channel chan);

/** ADE7816 zero-crossing detection timeout value function. */
int ade7816_zx_timeout(struct ade7816_desc *desc, uint32_t timeout_us);

/** ADE7816 set peak detection for channel function. */
int ade7816_peak_detect(struct ade7816_desc *desc, enum ade7816_channel chan, enum ade7816_mmode_sel mmode, uint8_t no_of_cycles);

/** ADE7816 set power direction for channel function. */
int ade7816_power_dir(struct ade7816_desc *desc, enum ade7816_channel chan);

/** ADE7816 get power direction for channel function. */
int ade7816_read_dir(struct ade7816_desc *desc, enum ade7816_channel chan, bool *sign);

/** ADE7816 angle measurement function. */
int ade7816_angle_meas(struct ade7816_desc *desc, enum ade7816_channel chan, enum ade7816_angle_sel sel);

/** ADE7816 read period value function. */
int ade7816_read_period(struct ade7816_desc *desc, uint32_t *period_us);

/** ADE7816 set interrupts function. */
int ade7816_set_interrupt(struct ade7816_desc *desc, enum ade7816_status_int status_int, bool enable);

/** ADE7816 get interrupts function. */
int ade7816_get_interrupt(struct ade7816_desc *desc, enum ade7816_status_int status_int, bool *enable);

/** ADE7816 root mean square measurement value conversion to microunits
 *  function.
 */
int ade7816_rms_to_micro(struct ade7816_desc *desc, enum ade7816_channel chan, uint32_t rms, uint32_t *micro);

/**
 * @brief enabled_config_inten
 *
 * @param desc
 * @param enable
 *
 * @return 0 in case of succes, negative error code otherwise.
 *
 */
int enabled_config_inten(struct ade7816_desc *desc, bool enable);


/** Initialize and configure the ADE7816 device. */
int ade7816_init(struct ade7816_desc **desc, struct ade7816_init_param *init_param);

#endif /* __ADE7816_H__ */
