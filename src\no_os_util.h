/**
 *   @file   no_os_util.h
 *   @brief  Header file of utility functions.
 *   <AUTHOR> (<EMAIL>)
 ********************************************************************************
 * Copyright 2018(c) Analog Devices, Inc.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice,
 *    this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * 3. Neither the name of Analog Devices, Inc. nor the names of its
 *    contributors may be used to endorse or promote products derived from this
 *    software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY ANALOG DEVICES, INC. “AS IS” AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
 * EVENT SHALL ANALOG DEVICES, INC. BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
 * OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *******************************************************************************/
#ifndef _NO_OS_UTIL_H_
#define _NO_OS_UTIL_H_

#include <stdint.h>
#include <stdlib.h>
#include <stdbool.h>
// #include "esp_err.h"
// #include "freertos/FreeRTOS.h"
// #include "freertos/task.h"
// #include "freertos/queue.h"
// #include "freertos/semphr.h"
// #include "freertos/timers.h"
// #include "freertos/portmacro.h"
// #include "freertos/projdefs.h"

#define NO_OS_BIT(x)                               (1 << (x))

#define NO_OS_BIT_ULL(x)                           ((uint64_t)1 << (x))

#define NO_OS_ARRAY_SIZE(x)                        (sizeof(x) / sizeof((x)[0]))

#define NO_OS_DIV_ROUND_UP(x, y)                   (((x) + (y) - 1) / (y))
#define NO_OS_DIV_ROUND_CLOSEST(x, y)              (((x) + (y) / 2) / (y))
#define NO_OS_DIV_ROUND_CLOSEST_ULL(x, y)          NO_OS_DIV_ROUND_CLOSEST(x, y)

#define no_os_min(x, y)                            (((x) < (y)) ? (x) : (y))
#define no_os_min_t(type, x, y)                    (type) no_os_min((type)(x), (type)(y))

#define no_os_max(x, y)                            (((x) > (y)) ? (x) : (y))
#define no_os_max_t(type, x, y)                    (type) no_os_max((type)(x), (type)(y))

#define no_os_clamp(val, min_val, max_val)         (no_os_max(no_os_min((val), (max_val)), (min_val)))
#define no_os_clamp_t(type, val, min_val, max_val) (type) no_os_clamp((type)(val), (type)(min_val), (type)(max_val))

#define no_os_swap(x, y)                                                                                                                                       \
    {                                                                                                                                                          \
        typeof(x) _tmp_ = (x);                                                                                                                                 \
        (x)             = (y);                                                                                                                                 \
        (y)             = _tmp_;                                                                                                                               \
    }

#define no_os_round_up(x, y) (((x) + (y) - 1) / (y))

#define NO_OS_BITS_PER_LONG  32

#define NO_OS_GENMASK(h, l)                                                                                                                                    \
    ({                                                                                                                                                         \
        uint32_t t = (uint32_t)(~0UL);                                                                                                                         \
        t          = t << (NO_OS_BITS_PER_LONG - (h - l + 1));                                                                                                 \
        t          = t >> (NO_OS_BITS_PER_LONG - (h + 1));                                                                                                     \
        t;                                                                                                                                                     \
    })
#define NO_OS_GENMASK_ULL(h, l)                                                                                                                                \
    ({                                                                                                                                                         \
        unsigned long long t = (unsigned long long)(~0ULL);                                                                                                    \
        t                    = t << (64 - (h - l + 1));                                                                                                        \
        t                    = t >> (64 - (h + 1));                                                                                                            \
        t;                                                                                                                                                     \
    })

#define no_os_bswap_constant_32(x) ((((x) & 0xff000000) >> 24) | (((x) & 0x00ff0000) >> 8) | (((x) & 0x0000ff00) << 8) | (((x) & 0x000000ff) << 24))

#define no_os_bswap_constant_16(x) ((((x) & (uint16_t)0xff00) >> 8) | (((x) & (uint16_t)0x00ff) << 8))

#define no_os_bit_swap_constant_8(x)                                                                                                                           \
    ((((x) & 0x80) >> 7) | (((x) & 0x40) >> 5) | (((x) & 0x20) >> 3) | (((x) & 0x10) >> 1) | (((x) & 0x08) << 1) | (((x) & 0x04) << 3) | (((x) & 0x02) << 5) | \
     (((x) & 0x01) << 7))

#define NO_OS_U16_MAX                       ((uint16_t)~0U)
#define NO_OS_S16_MAX                       ((int16_t)(NO_OS_U16_MAX >> 1))

#define NO_OS_DIV_U64(x, y)                 (x / y)

#define NO_OS_UNUSED_PARAM(x)               ((void)x)

#define no_os_shift_right(x, s)             ((x) < 0 ? -(-(x) >> (s)) : (x) >> (s))

#define no_os_align(x, align)               (((x) + ((typeof(x))(align) - 1)) & ~((typeof(x))(align) - 1))

#define no_os_bcd2bin(x)                    (((x) & 0x0f) + ((x) >> 4) * 10)
#define no_os_bin2bcd(x)                    ((((x) / 10) << 4) + (x) % 10)

#define NO_OS_CONTAINER_OF(ptr, type, name) ((type *)((char *)(ptr) - offsetof(type, name)))

inline void no_os_mdelay(uint32_t ms)
{
    // vTaskDelay(pdMS_TO_TICKS(ms));
    delay(ms);
}

/* Check if bit set */
inline int no_os_test_bit(int pos, const volatile void *addr)
{
    return (((const int *)addr)[pos / 32] >> pos) & 1UL;
}

/**
 * Find first set bit in word.
 */
inline uint32_t no_os_find_first_set_bit(uint32_t word)
{
    uint32_t first_set_bit = 0;

    while (word)
    {
        if (word & 0x1)
            return first_set_bit;
        word >>= 1;
        first_set_bit++;
    }

    return 32;
}

/**
 * Find first set bit in word.
 */
inline uint64_t no_os_find_first_set_bit_u64(uint64_t word)
{
    uint64_t first_set_bit = 0;

    while (word)
    {
        if (word & 0x1)
            return first_set_bit;
        word >>= 1;
        first_set_bit++;
    }

    return 64;
}

/**
 * Find last set bit in word.
 */
inline uint32_t no_os_find_last_set_bit(uint32_t word)
{
    uint32_t bit          = 0;
    uint32_t last_set_bit = 32;

    while (word)
    {
        if (word & 0x1)
            last_set_bit = bit;
        word >>= 1;
        bit++;
    }

    return last_set_bit;
}

/**
 * Locate the closest element in an array.
 */
inline uint32_t no_os_find_closest(int32_t val, const int32_t *array, uint32_t size)
{
    int32_t  diff = abs(array[0] - val);
    uint32_t ret  = 0;
    uint32_t i;

    for (i = 1; i < size; i++)
    {
        if (abs(array[i] - val) < diff)
        {
            diff = abs(array[i] - val);
            ret  = i;
        }
    }

    return ret;
}

/**
 * Shift the value and apply the specified mask.
 */
inline uint32_t no_os_field_prep(uint32_t mask, uint32_t val)
{
    return (val << no_os_find_first_set_bit(mask)) & mask;
}

inline uint64_t no_os_field_prep_u64(uint64_t mask, uint64_t val)
{
    return (val << no_os_find_first_set_bit_u64(mask)) & mask;
}

/**
 * Get a field specified by a mask from a word.
 */
inline uint32_t no_os_field_get(uint32_t mask, uint32_t word)
{
    return (word & mask) >> no_os_find_first_set_bit(mask);
}

/**
 * Produce the maximum value representable by a field
 */
inline uint32_t no_os_field_max(uint32_t mask)
{
    // Find the first set bit to determine the shift position
    uint32_t first_set_bit = no_os_find_first_set_bit(mask);

    // Shift the mask to the right by the position of the first set bit
    uint32_t shifted_mask = mask >> first_set_bit;

    return shifted_mask;
}

/**
 * Produce the maximum value representable by a field
 */
inline uint64_t no_os_field_max_u64(uint64_t mask)
{
    // Find the first set bit to determine the shift position
    uint64_t first_set_bit = no_os_find_first_set_bit_u64(mask);

    // Shift the mask to the right by the position of the first set bit
    uint64_t shifted_mask = mask >> first_set_bit;

    return shifted_mask;
}

/**
 * Log base 2 of the given number.
 */
inline int32_t no_os_log_base_2(uint32_t x)
{
    return no_os_find_last_set_bit(x);
}

/**
 * Find greatest common divisor of the given two numbers.
 */
inline uint32_t no_os_greatest_common_divisor(uint32_t a, uint32_t b)
{
    uint32_t div;

    if ((a == 0) || (b == 0))
        return no_os_max(a, b);

    while (b != 0)
    {
        div = a % b;
        a   = b;
        b   = div;
    }

    return a;
}

inline uint64_t no_os_greatest_common_divisor_u64(uint64_t a, uint64_t b)
{
    uint64_t div;

    if ((a == 0) || (b == 0))
        return no_os_max(a, b);

    while (b != 0)
    {
        div = a % b;
        a   = b;
        b   = div;
    }

    return a;
}

/**
 * Find lowest common multiple of the given two numbers.
 */
inline uint32_t no_os_lowest_common_multiple(uint32_t a, uint32_t b)
{
    if (a && b)
        return (a / no_os_greatest_common_divisor(a, b)) * b;
    else
        return 0;
}

/**
 * Calculate best rational approximation for a given fraction.
 */
inline void no_os_rational_best_approximation(uint32_t given_numerator, uint32_t given_denominator, uint32_t max_numerator, uint32_t max_denominator,
                                              uint32_t *best_numerator, uint32_t *best_denominator)
{
    uint32_t gcd;

    gcd = no_os_greatest_common_divisor(given_numerator, given_denominator);

    *best_numerator   = given_numerator / gcd;
    *best_denominator = given_denominator / gcd;

    if ((*best_numerator > max_numerator) || (*best_denominator > max_denominator))
    {
        *best_numerator   = 0;
        *best_denominator = 0;
    }
}

inline void no_os_rational_best_approximation_u64(uint64_t given_numerator, uint64_t given_denominator, uint64_t max_numerator, uint64_t max_denominator,
                                                  uint64_t *best_numerator, uint64_t *best_denominator)
{
    uint64_t gcd;

    gcd = no_os_greatest_common_divisor_u64(given_numerator, given_denominator);

    *best_numerator   = given_numerator / gcd;
    *best_denominator = given_denominator / gcd;

    if ((*best_numerator > max_numerator) || (*best_denominator > max_denominator))
    {
        *best_numerator   = 0;
        *best_denominator = 0;
    }
}

/**
 * Calculate the number of set bits (8-bit size).
 */
inline unsigned int no_os_hweight8(uint8_t word)
{
    uint32_t count = 0;

    while (word)
    {
        if (word & 0x1)
            count++;
        word >>= 1;
    }

    return count;
}

/**
 * Calculate the number of set bits (16-bit size).
 */
inline unsigned int no_os_hweight16(uint16_t word)
{
    return no_os_hweight8(word >> 8) + no_os_hweight8(word);
}

/**
 * Calculate the number of set bits (32-bit size).
 */
inline unsigned int no_os_hweight32(uint32_t word)
{
    return no_os_hweight16(word >> 16) + no_os_hweight16(word);
}

/**
 * Calculate the quotient and the remainder of an integer division.
 */
inline uint64_t no_os_do_div(uint64_t *n, uint64_t base)
{
    uint64_t mod = 0;

    mod = *n % base;
    *n  = *n / base;

    return mod;
}

/**
 * Unsigned 64bit divide with 64bit divisor and remainder
 */
inline uint64_t no_os_div64_u64_rem(uint64_t dividend, uint64_t divisor, uint64_t *remainder)
{
    *remainder = dividend % divisor;

    return dividend / divisor;
}

/**
 * Unsigned 64bit divide with 32bit divisor with remainder
 */
inline uint64_t no_os_div_u64_rem(uint64_t dividend, uint32_t divisor, uint32_t *remainder)
{
    *remainder = no_os_do_div(&dividend, divisor);

    return dividend;
}

/**
 * Signed 64bit divide with 32bit divisor with remainder
 */
inline int64_t no_os_div_s64_rem(int64_t dividend, int32_t divisor, int32_t *remainder)
{
    *remainder = dividend % divisor;
    return dividend / divisor;
}

/**
 * Unsigned 64bit divide with 32bit divisor
 */
inline uint64_t no_os_div_u64(uint64_t dividend, uint32_t divisor)
{
    uint32_t remainder;

    return no_os_div_u64_rem(dividend, divisor, &remainder);
}

/**
 * Signed 64bit divide with 32bit divisor
 */
inline int64_t no_os_div_s64(int64_t dividend, int32_t divisor)
{
    int32_t remainder;
    return no_os_div_s64_rem(dividend, divisor, &remainder);
}

/**
 * Converts from string to int32_t
 * @param *str
 * @return int32_t
 */
inline int32_t no_os_str_to_int32(const char *str)
{
    char   *end;
    int32_t value = strtol(str, &end, 0);

    if (end == str)
        return ESP_FAIL;
    else
        return value;
}

/**
 * Converts from string to uint32_t
 * @param *str
 * @return uint32_t
 */
inline uint32_t no_os_str_to_uint32(const char *str)
{
    char    *end;
    uint32_t value = strtoul(str, &end, 0);

    if (end == str)
        return ESP_FAIL;
    else
        return value;
}

inline void no_os_put_unaligned_be16(uint16_t val, uint8_t *buf)
{
    buf[1] = val & 0xFF;
    buf[0] = val >> 8;
}

inline uint16_t no_os_get_unaligned_be16(uint8_t *buf)
{
    return buf[1] | ((uint16_t)buf[0] << 8);
}

inline void no_os_put_unaligned_le16(uint16_t val, uint8_t *buf)
{
    buf[0] = val & 0xFF;
    buf[1] = val >> 8;
}

inline uint16_t no_os_get_unaligned_le16(uint8_t *buf)
{
    return buf[0] | ((uint16_t)buf[1] << 8);
}

inline void no_os_put_unaligned_be24(uint32_t val, uint8_t *buf)
{
    buf[2] = val & 0xFF;
    buf[1] = (val >> 8) & 0xFF;
    buf[0] = val >> 16;
}

inline uint32_t no_os_get_unaligned_be24(uint8_t *buf)
{
    return buf[2] | ((uint16_t)buf[1] << 8) | ((uint32_t)buf[0] << 16);
}

inline void no_os_put_unaligned_le24(uint32_t val, uint8_t *buf)
{
    buf[0] = val & 0xFF;
    buf[1] = (val >> 8) & 0xFF;
    buf[2] = val >> 16;
}

inline uint32_t no_os_get_unaligned_le24(uint8_t *buf)
{
    return buf[0] | ((uint16_t)buf[1] << 8) | ((uint32_t)buf[2] << 16);
}

inline void no_os_put_unaligned_be32(uint32_t val, uint8_t *buf)
{
    buf[3] = val & 0xFF;
    buf[2] = (val >> 8) & 0xFF;
    buf[1] = (val >> 16) & 0xFF;
    buf[0] = val >> 24;
}

inline uint32_t no_os_get_unaligned_be32(uint8_t *buf)
{
    return buf[3] | ((uint16_t)buf[2] << 8) | ((uint32_t)buf[1] << 16) | ((uint32_t)buf[0] << 24);
}

inline void no_os_put_unaligned_le32(uint32_t val, uint8_t *buf)
{
    buf[0] = val & 0xFF;
    buf[1] = (val >> 8) & 0xFF;
    buf[2] = (val >> 16) & 0xFF;
    buf[3] = val >> 24;
}

inline uint32_t no_os_get_unaligned_le32(uint8_t *buf)
{
    return buf[0] | ((uint16_t)buf[1] << 8) | ((uint32_t)buf[2] << 16) | ((uint32_t)buf[3] << 24);
}

inline int16_t no_os_sign_extend16(uint16_t value, int index)
{
    uint8_t shift = 15 - index;
    return (int16_t)(value << shift) >> shift;
}

inline int32_t no_os_sign_extend32(uint32_t value, int index)
{
    uint8_t shift = 31 - index;
    return (int32_t)(value << shift) >> shift;
}

inline uint64_t no_os_mul_u32_u32(uint32_t a, uint32_t b)
{
    return (uint64_t)a * b;
}

inline uint64_t no_os_mul_u64_u32_shr(uint64_t a, uint32_t mul, unsigned int shift)
{
    uint32_t ah, al;
    uint64_t ret;

    al = a;
    ah = a >> 32;

    ret = no_os_mul_u32_u32(al, mul) >> shift;
    if (ah)
        ret += no_os_mul_u32_u32(ah, mul) << (32 - shift);

    return ret;
}

inline uint64_t no_os_mul_u64_u32_div(uint64_t a, uint32_t mul, uint32_t divisor)
{
    int      i;
    uint64_t low, high, temp, rem;
    uint32_t a_high   = a >> 32;
    uint32_t a_low    = a & 0xFFFFFFFF;
    uint64_t result   = 0;
    uint64_t low_low  = no_os_mul_u32_u32(a_low, mul);
    uint64_t high_low = no_os_mul_u32_u32(a_high, mul);

    low  = low_low + ((high_low & 0xFFFFFFFF) << 32);
    high = (high_low >> 32) + (low_low >> 32);

    rem = high;

    for (i = 0; i < 64; i++)
    {
        /* Shift remainder left and add the next bit from low */
        rem = (rem << 1) | (low >> 63);
        low <<= 1;

        /* Compare the remainder with the divisor */
        if (rem >= divisor)
        {
            rem -= divisor;
            temp = (uint64_t)1 << (63 - i);
            result |= temp;
        }
    }
    return result;
}

/**
 * @brief Check big endianess of the host processor.
 * @return Big endianess status (true/false)
 */
inline bool no_os_is_big_endian(void)
{
    uint16_t a = 0x0100;
    return (bool)*(uint8_t *)&a;
}

/* @brief Swap bytes in a buffer with a given step
 *        Swap with step of 2:
 *        AA BB CC DD EE FF 00 11 becomes
 *        BB AA DD CC FF EE 11 00
 *        Swap with step of 3:
 *        AA BB CC DD EE FF 00 11 22 becomes
 *        CC BB AA FF EE DD 22 11 00
 *        etc.
 * @param buf - Input buffer to be swapped.
 * @param bytes - Number of bytes.
 * @param step - Number of steps.
 * @return None
 */
inline void no_os_memswap64(void *buf, uint32_t bytes, uint32_t step)
{
    uint8_t *p = buf;
    uint32_t i, j;
    uint8_t  temp[8];

    if (step < 2 || step > 8 || bytes < step || bytes % step != 0)
        return;

    for (i = 0; i < bytes; i += step)
    {
        memcpy(temp, p, step);
        for (j = step; j > 0; j--)
        {
            *p++ = temp[j - 1];
        }
    }
}

#endif  // _NO_OS_UTIL_H_
