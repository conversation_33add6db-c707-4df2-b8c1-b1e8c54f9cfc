#include "ade7816_i2c.h"
#include "Wire.h"
#include "Arduino.h"

RTTStream rtt;
// 声明全局变量
Ade7816* ade7816 = nullptr;


// 定义传感器数据结构体
typedef struct
{
    uint32_t Voltage_RMS;
    uint32_t Current_RMS;
    float    Voltage;
    float    Current;
    float    Active_Power;
    float    Reactive_Power;
} sensor_data_t;

uint32_t             rms, scaled;
uint32_t              val;

int32_t  active_w, reactive_var;
uint32_t vrms, irms;
float    voltage, current;

void setup()
{
  rtt.println("Wire initialized");
  ade7816 = new Ade7816();
  ade7816->ade7816_init();

  ade7816->enabled_config_inten(true);

  ade7816->ade7816_zx_detect(ADE7816_CHANNEL_A);

  ade7816->ade7816_set_phase(ADE7816_CHANNEL_A, ADE7816_PCF_50HZ);
  ade7816->ade7816_set_phase(ADE7816_CHANNEL_B, ADE7816_PCF_50HZ);
  ade7816->ade7816_set_phase(ADE7816_CHANNEL_C, ADE7816_PCF_50HZ);
  ade7816->ade7816_set_phase(ADE7816_CHANNEL_D, ADE7816_PCF_50HZ);
  ade7816->ade7816_set_phase(ADE7816_CHANNEL_E, ADE7816_PCF_50HZ);
  ade7816->ade7816_set_phase(ADE7816_CHANNEL_F, ADE7816_PCF_50HZ);
  ade7816->ade7816_set_active_thr(8000);
  ade7816->ade7816_set_reactive_thr(8000);
  ade7816->ade7816_reg_update(ADE7816_MASK0_REG, NO_OS_BIT(10), NO_OS_BIT(10));
  ade7816->ade7816_write_reg(ADE7816_DICOEFF_REG, 0xFFF8000);
  ade7816->ade7816_reg_update(ADE7816_LCYCMODE_REG, ADE7816_RSTREAD_MASK, no_os_field_prep(ADE7816_RSTREAD_MASK, 0));
  ade7816->lcycle_mode = false;
  ade7816->ade7816_i2c_reg_write(ADE7816_STATUS0_REG, 0);
  ade7816->ade7816_i2c_reg_read(ADE7816_MASK0_REG, &rms);
}

void loop()
{
  delay(1000);  // Update every second

  // Fix error handling and translate Chinese to English
  int32_t ret;  // Add missing ret declaration
  
  ret = ade7816->ade7816_read_active_energy(ADE7816_CHANNEL_A, &val);
  if (ret != 0) {
    rtt.println("Error reading active energy");
  }

  rtt.printf("Active Energy RAW: %ld\n", val);


  ret = ade7816->ade7816_read_reactive_energy(ADE7816_CHANNEL_A, &val);
  if (ret != 0) {
    rtt.println("Error reading reactive energy");
  }
  rtt.printf("Reactive Energy RAW: %ld\n", val);

  ret = ade7816->ade7816_read_rms(ADE7816_CHANNEL_A, &rms);
  if (ret != 0) {
    rtt.println("Error reading current RMS");
  }
  rtt.printf("RMS = %d", rms);


  ret = ade7816->ade7816_rms_to_micro(ADE7816_CHANNEL_A, rms, &scaled);
  if (ret != 0) {
    rtt.println("Error converting current RMS");
  }

  current = (float)scaled / 1000000;  // Convert to amperes
  rtt.printf("IA_RMS: %ld uA  %.3f A\n", scaled, current);

  ret = ade7816->ade7816_read_rms(ADE7816_CHANNEL_VOLTAGE, &rms);
  if (ret != 0) {
    rtt.println("Error reading voltage RMS");
  }

  ret = ade7816->ade7816_rms_to_micro(ADE7816_CHANNEL_VOLTAGE, rms, &scaled);
  if (ret != 0) {
    rtt.println("Error converting voltage RMS");
  }

  voltage = (float)scaled / 1000000;  // Convert to volts
  rtt.printf("V_RMS: %ld uV  %.3f V\n", scaled, voltage);

  rtt.println("----------------------------------------");


}


