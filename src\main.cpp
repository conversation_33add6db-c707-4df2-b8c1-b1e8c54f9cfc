#include "ade7816_i2c.h"
#include "Wire.h"
#include "Arduino.h"
#include <RTTStream.h>
RTTStream rtt;
// 声明全局变量
Ade7816* ade7816 = nullptr;

void setup()
{
ade7816 = new Ade7816(Wire);
ade7816_desc** desc;
rtt.println("ade7816_desc** desc;");
ade7816->ade7816_init(desc);
rtt.println("ade7816->ade7816_init(desc)");
// pinMode(35,OUTPUT);
// pinMode(7,OUTPUT);
// pinMode(SX126X_CS,OUTPUT);
// pinMode(SX126X_DIO1,OUTPUT);
// pinMode(SX126X_BUSY,OUTPUT);
// pinMode(SX126X_RESET,OUTPUT);
}
void loop() 
{
  rtt.println("ade7816 = new Ade7816(Wire);");
  delay(1000);
}


