/*
  RadioLib SX126x Ping-Pong Example

  This example is intended to run on two SX126x radios,
  and send packets between the two.

  For default module settings, see the wiki page
  https://github.com/jgromes/RadioLib/wiki/Default-configuration#sx126x---lora-modem

  For full API reference, see the GitHub Pages
  https://jgromes.github.io/RadioLib/
*/

// include the library
#include <RadioLib.h>
// uncomment the following only on one
// of the nodes to initiate the pings
// #define INITIATING_NODE

// SX1262 has the following connections:

// #define USE_SX1262
// #define SX126X_CS (34)
// #define SX126X_DIO1 (47)
// #define SX126X_BUSY (16)
// #define SX126X_RESET (40)
// #define PIN_SPI_MISO (11)
// #define PIN_SPI_MOSI (35)
// #define PIN_SPI_SCK (7)

#define INITIATING_NODE   

#include <RadioLib.h>
SX1262 radio = new Module(SX126X_CS, SX126X_DIO1, SX126X_RESET, SX126X_BUSY);
int transmissionState = RADIOLIB_ERR_NONE;
bool transmitFlag = false;
volatile bool operationDone = false;


#include "ade7816_i2c.h"
#include "Wire.h"

void setup() 
{
pinMode(30,OUTPUT);
ade7816 = new Ade7816(Wire);
// pinMode(35,OUTPUT);
// pinMode(7,OUTPUT);
// pinMode(SX126X_CS,OUTPUT);
// pinMode(SX126X_DIO1,OUTPUT);
// pinMode(SX126X_BUSY,OUTPUT);
// pinMode(SX126X_RESET,OUTPUT);
}
void loop() 
{
  // Toggle both LEDs every 1 second
  digitalToggle(30);
  // digitalToggle(PIN_SPI_MOSI);
  // digitalToggle(PIN_SPI_SCK);
  // digitalToggle(SX126X_CS);
  // digitalToggle(SX126X_DIO1);
  // digitalToggle(SX126X_BUSY);
  // digitalToggle(SX126X_RESET);
  delay(1000);
}

// #if defined(ESP8266) || defined(ESP32)
//   ICACHE_RAM_ATTR
// #endif
// void setFlag(void) {
//   // we sent or received a packet, set the flag
//   operationDone = true;
//   // delay(1000);
// }
// #define RF_SWITCH_MODE 35 // HIGH SINGLE MODE
// #define FEM_EN 34
// #define SX126X_TXEN CPS 

// void setup() {
// #ifdef RF_SWITCH_MODE
//   pinMode(RF_SWITCH_MODE, OUTPUT);
//   digitalWrite(RF_SWITCH_MODE, HIGH);
// #endif

// #ifdef FEM_EN
//   pinMode(FEM_EN, OUTPUT);
//   digitalWrite(FEM_EN, HIGH);
// #endif

//   Serial.begin(115200);
//   // initialize SX1262 with default settings
//   Serial.print(F("[SX1262] Initializing ... "));
//   int state = radio.begin();
//   if (state == RADIOLIB_ERR_NONE) {
//     Serial.println(F("success!"));
//   } else {
//     Serial.print(F("failed, code "));
//     Serial.println(state);
//     while (true) { delay(10); }
//   }

//   // set the function that will be called
//   // when new packet is received
//   radio.setDio1Action(setFlag);
//   radio.setOutputPower(22);
//   radio.setDio2AsRfSwitch();
//   radio.setRxBoostedGainMode(true);

//   #if defined(INITIATING_NODE)
//     // send the first packet on this node
//     Serial.print(F("[SX1262] Sending first packet ... "));
//     transmissionState = radio.startTransmit("Hello World!");
//     transmitFlag = true;
//   #else
//     // start listening for LoRa packets on this node
//     Serial.print(F("[SX1262] Starting to listen ... "));
//     state = radio.startReceive();
//     if (state == RADIOLIB_ERR_NONE) {
//       Serial.println(F("success!"));
//     } else {
//       Serial.print(F("failed, code "));
//       Serial.println(state);
//       while (true) { delay(10); }
//     }
//   #endif
// }
// void loop() {
//   // check if the previous operation finished
//   // Serial.print(F("[SX1262] Sending first packet ... "));
//   // transmissionState = radio.startTransmit("Hello World!");
//   // transmitFlag = true;
//   // Serial.print(F("loop"));
//   if(operationDone) {
//     // reset flag
//     operationDone = false;

//     if(transmitFlag) {
//       // the previous operation was transmission, listen for response
//       // print the result
//       if (transmissionState == RADIOLIB_ERR_NONE) {
//         // packet was successfully sent
//         Serial.println(F("transmission finished!"));

//       } else {
//         Serial.print(F("failed, code "));
//         Serial.println(transmissionState);

//       }

//       // listen for response
//       radio.startReceive();
//       transmitFlag = false;

//     } else {
//       // the previous operation was reception
//       // print data and send another packet
//       String str;
//       int state = radio.readData(str);

//       if (state == RADIOLIB_ERR_NONE) {
//         // packet was successfully received
//         Serial.println(F("[SX1262] Received packet!"));

//         // print data of the packet
//         Serial.print(F("[SX1262] Data:\t\t"));
//         Serial.println(str);

//         // print RSSI (Received Signal Strength Indicator)
//         Serial.print(F("[SX1262] RSSI:\t\t"));
//         Serial.print(radio.getRSSI());
//         Serial.println(F(" dBm"));

//         // print SNR (Signal-to-Noise Ratio)
//         Serial.print(F("[SX1262] SNR:\t\t"));
//         Serial.print(radio.getSNR());
//         Serial.println(F(" dB"));

//       }

//       // wait a second before transmitting again
//       delay(1000);

//       // send another one
//       Serial.print(F("[SX1262] Sending another packet ... "));
//       transmissionState = radio.startTransmit("Hello World! gateway ");
//       transmitFlag = true;
//     }
  
//   }
// }
