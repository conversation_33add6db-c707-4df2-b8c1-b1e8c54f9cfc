#include "ade7816_i2c.h"
#include "Wire.h"
#include "Arduino.h"
#include <RTTStream.h>
RTTStream rtt;
// 声明全局变量
Ade7816* ade7816 = nullptr;
struct ade7816_desc *ade7816_desc = nullptr;

// 定义传感器数据结构体
typedef struct
{
    uint32_t Voltage_RMS;
    uint32_t Current_RMS;
    float    Voltage;
    float    Current;
    float    Active_Power;
    float    Reactive_Power;
} sensor_data_t;

static sensor_data_t collected_sensors;

void setup()
{
  Wire.begin();
  rtt.println("Wire initialized");

  ade7816 = new Ade7816(Wire);
  if (!ade7816) {
    rtt.println("ERROR: Failed to create Ade7816 instance");
    while(1); // 停止执行
  }
  rtt.println("Ade7816 instance created");

  int ret = ade7816->ade7816_init(&ade7816_desc);
  if (ret != 0) {
    rtt.printf("ERROR: ade7816_init failed with code: %d\n", ret);
    while(1); // 停止执行
  }

  if (!ade7816_desc) {
    rtt.println("ERROR: ade7816_desc is null after init");
    while(1); // 停止执行
  }
  rtt.println("ade7816_init successful");

  rtt.println("Starting configuration...");

  ret = ade7816->enabled_config_inten(ade7816_desc, true);
  if (ret != 0) {
    rtt.printf("ERROR: enabled_config_inten failed: %d\n", ret);
    while(1);
  }
  rtt.println("enabled_config_inten OK");

  ret = ade7816->ade7816_zx_detect(ade7816_desc, ADE7816_CHANNEL_A);
  if (ret != 0) {
    rtt.printf("ERROR: ade7816_zx_detect failed: %d\n", ret);
    while(1);
  }
  rtt.println("ade7816_zx_detect OK");

  // 暂时注释掉复杂的配置，先确保基本初始化工作
  /*
  ade7816->ade7816_set_phase(ade7816_desc, ADE7816_CHANNEL_A, ADE7816_PCF_50HZ);
  ade7816->ade7816_set_phase(ade7816_desc, ADE7816_CHANNEL_B, ADE7816_PCF_50HZ);
  ade7816->ade7816_set_phase(ade7816_desc, ADE7816_CHANNEL_C, ADE7816_PCF_50HZ);
  ade7816->ade7816_set_phase(ade7816_desc, ADE7816_CHANNEL_D, ADE7816_PCF_50HZ);
  ade7816->ade7816_set_phase(ade7816_desc, ADE7816_CHANNEL_E, ADE7816_PCF_50HZ);
  ade7816->ade7816_set_phase(ade7816_desc, ADE7816_CHANNEL_F, ADE7816_PCF_50HZ);
  ade7816->ade7816_set_active_thr(ade7816_desc, 8000);
  ade7816->ade7816_set_reactive_thr(ade7816_desc, 8000);
  ade7816->ade7816_reg_update(ade7816_desc, ADE7816_MASK0_REG, NO_OS_BIT(10), NO_OS_BIT(10));
  ade7816->ade7816_write_reg(ade7816_desc, ADE7816_DICOEFF_REG, 0xFFF8000);
  ade7816->ade7816_reg_update(ade7816_desc, ADE7816_LCYCMODE_REG, ADE7816_RSTREAD_MASK, no_os_field_prep(ADE7816_RSTREAD_MASK, 0));
  ade7816_desc->lcycle_mode = false;
  ade7816->ade7816_write_reg(ade7816_desc, ADE7816_STATUS0_REG, 0);
  uint32_t rms;
  ade7816->ade7816_read_reg(ade7816_desc, ADE7816_MASK0_REG, &rms);
  */

  rtt.println("Setup completed successfully!");
// rtt.println("ade7816->ade7816_init(desc)");
// pinMode(35,OUTPUT);
// pinMode(7,OUTPUT);
// pinMode(SX126X_CS,OUTPUT);
// pinMode(SX126X_DIO1,OUTPUT);
// pinMode(SX126X_BUSY,OUTPUT);
// pinMode(SX126X_RESET,OUTPUT);
}
void loop()
{
  // 安全检查
  if (!ade7816 || !ade7816_desc) {
    rtt.println("ERROR: ade7816 or ade7816_desc is null in loop");
    delay(1000);
    return;
  }

  int32_t  active_w, reactive_var;
  uint32_t vrms, irms;
  float    voltage, current;
  uint32_t             rms, scaled;
  int32_t              val;
  rtt.printf(  "----------------------------------------");
  rtt.printf(  "channel A:");

  // 简化测试，先只读取一个寄存器
  uint32_t test_val;
  int ret = ade7816->ade7816_read_reg(ade7816_desc, ADE7816_RUN_REG, &test_val);
  if (ret != 0) {
    rtt.printf("ERROR: ade7816_read_reg failed with code: %d\n", ret);
  } else {
    rtt.printf("RUN_REG value: 0x%08lX\n", test_val);
  }

  // ade7816->ade7816_read_reactive_energy(ade7816_desc, ADE7816_CHANNEL_A, &val);

  // rtt.printf(  "wugonggonglv RAW: %ld", val);
  // reactive_var = val;

  // ade7816->ade7816_read_rms(ade7816_desc, ADE7816_CHANNEL_A, &rms);

  // ade7816->ade7816_rms_to_micro(ade7816_desc, ADE7816_CHANNEL_A, rms, &scaled);

  // current = (float)scaled / 1000000;  // 转换为电流值，单位为安培

  // rtt.printf(  "IA_RMS: %ld uA  %f A", scaled, current);
  // irms = scaled;

  // ade7816->ade7816_read_rms(ade7816_desc, ADE7816_CHANNEL_VOLTAGE, &rms);

  // ade7816->ade7816_rms_to_micro(ade7816_desc, ADE7816_CHANNEL_VOLTAGE, rms, &scaled);

  // voltage = (float)scaled / 1000000;  // 转换为电压值，单位为伏特

  // rtt.printf(  "V_RMS: %ld uV  %f V", scaled, voltage);
  // vrms = scaled;

  // rtt.printf(  "----------------------------------------");

  // collected_sensors.Voltage_RMS    = vrms;
  // collected_sensors.Current_RMS    = irms;
  // collected_sensors.Voltage        = voltage;
  // collected_sensors.Current        = current;
  // collected_sensors.Active_Power   = active_w;
  // collected_sensors.Reactive_Power = reactive_var;

  delay(1000);  // Update every second

}


