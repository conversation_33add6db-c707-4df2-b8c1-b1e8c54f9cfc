#include "ade7816_i2c.h"
#include "Wire.h"
#include "Arduino.h"

// 声明全局变量
Ade7816* ade7816 = nullptr;

void setup()
{
pinMode(30,OUTPUT);
ade7816 = new Ade7816(Wire);
// pinMode(35,OUTPUT);
// pinMode(7,OUTPUT);
// pinMode(SX126X_CS,OUTPUT);
// pinMode(SX126X_DIO1,OUTPUT);
// pinMode(SX126X_BUSY,OUTPUT);
// pinMode(SX126X_RESET,OUTPUT);
}
void loop() 
{
  // Toggle both LEDs every 1 second
  digitalToggle(30);
  // digitalToggle(PIN_SPI_MOSI);
  // digitalToggle(PIN_SPI_SCK);
  // digitalToggle(SX126X_CS);
  // digitalToggle(SX126X_DIO1);
  // digitalToggle(SX126X_BUSY);
  // digitalToggle(SX126X_RESET);
  delay(1000);
}


