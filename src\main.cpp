#include "ade7816_i2c.h"
#include "Wire.h"
#include "Arduino.h"
#include <RTTStream.h>
RTTStream rtt;
// 声明全局变量
Ade7816* ade7816 = nullptr;

void setup()
{
  Wire.begin();
ade7816 = new Ade7816(Wire);
struct ade7816_desc *ade7816_desc;
rtt.println("ade7816_desc** desc;");
ade7816->ade7816_init(&ade7816_desc);

ret = enabled_config_inten(ade7816_desc, true);
ESP_ERROR_CHECK(ret);

ret = ade7816_zx_detect(ade7816_desc, ADE7816_CHANNEL_A);
ESP_ERROR_CHECK(ret);

ret = ade7816_set_phase(ade7816_desc, ADE7816_CHANNEL_A, ADE7816_PCF_50HZ);
ESP_ERROR_CHECK(ret);

ret = ade7816_set_phase(ade7816_desc, ADE7816_CHANNEL_B, ADE7816_PCF_50HZ);
ESP_ERROR_CHECK(ret);

ret = ade7816_set_phase(ade7816_desc, ADE7816_CHANNEL_C, ADE7816_PCF_50HZ);
ESP_ERROR_CHECK(ret);

ret = ade7816_set_phase(ade7816_desc, ADE7816_CHANNEL_D, ADE7816_PCF_50HZ);
ESP_ERROR_CHECK(ret);

ret = ade7816_set_phase(ade7816_desc, ADE7816_CHANNEL_E, ADE7816_PCF_50HZ);
ESP_ERROR_CHECK(ret);

ret = ade7816_set_phase(ade7816_desc, ADE7816_CHANNEL_F, ADE7816_PCF_50HZ);
ESP_ERROR_CHECK(ret);

ret = ade7816_set_active_thr(ade7816_desc, 8000);
ESP_ERROR_CHECK(ret);

ret = ade7816_set_reactive_thr(ade7816_desc, 8000);
ESP_ERROR_CHECK(ret);

ret = ade7816_reg_update(ade7816_desc, ADE7816_MASK0_REG, NO_OS_BIT(10), NO_OS_BIT(10));
ESP_ERROR_CHECK(ret);

ret = ade7816_write_reg(ade7816_desc, ADE7816_DICOEFF_REG, 0xFFF8000);
ESP_ERROR_CHECK(ret);

ret = ade7816_reg_update(ade7816_desc, ADE7816_LCYCMODE_REG, ADE7816_RSTREAD_MASK, no_os_field_prep(ADE7816_RSTREAD_MASK, 0));
ESP_ERROR_CHECK(ret);

ade7816_desc->lcycle_mode = false;

ret = ade7816_write_reg(ade7816_desc, ADE7816_STATUS0_REG, 0);
ESP_ERROR_CHECK(ret);

ret = ade7816_read_reg(ade7816_desc, ADE7816_MASK0_REG, &rms);
ESP_ERROR_CHECK(ret);
// rtt.println("ade7816->ade7816_init(desc)");
// pinMode(35,OUTPUT);
// pinMode(7,OUTPUT);
// pinMode(SX126X_CS,OUTPUT);
// pinMode(SX126X_DIO1,OUTPUT);
// pinMode(SX126X_BUSY,OUTPUT);
// pinMode(SX126X_RESET,OUTPUT);
}
void loop() 
{
  rtt.println("ade7816 = new Ade7816(Wire);");
  delay(1000);
  digitalToggle(32);
  digitalToggle(22);
}


